// Node Modules & Site Config
const fs = require("fs");
const path = require("path");
const cors = require("cors");
const fse = require("fs-extra");
const express = require("express");
const app = express();
const chokidar = require("chokidar");
const execSync = require("child_process").execSync;
const { exec } = require("child_process");
const source = path.dirname(fs.realpathSync(__filename));
const sitePath = process.cwd();
const config = require(`${sitePath}/config`);
const HeadlessFlash = require("./../system/headless-flash");
const scriptHooks = require("./../system/script-hooks");

// Port Definition
var port = process.env.PORT || 4000;
process.argv.forEach((argument) => {
	if (argument.indexOf("--port=") >= 0)
		port = parseInt(argument.replace("--port=", ""));
});

// Execute prewatch hook and then start watching
(async () => {
	try {
		await scriptHooks.prewatch();
	} catch (error) {
		console.error("Warning: prewatch hook failed:", error.message);
	}

	// Build the website first
	process.stdout.write("Building the website...\n");
	exec(`node ${source}/build`, (error, stdout, stderr) => {
	if (error || stderr) {
		console.log(stdout);
		console.log("\x1b[31m", "Done with errors :(", "\x1b[0m\n");
		console.log(`\x1b[31m${stderr}`, "\x1b[0m\n");
	} else {
		console.log(stdout);
		console.log("\x1b[32m", "Done with success!", "\x1b[0m\n");
	}

	// Host the website locally
	process.stdout.write("Starting Local Server...");
	app.use(cors());
	app.use(express.static(config.build.folder));
	app.get("*", (req, res) => {
		res.status(404).sendFile(`${sitePath}/build/404.html`);
	});
	app.listen(port);
	process.stdout.write(` Started on http://localhost:${port}/\n`);

	// Watch for changes
	console.log("Watching for changes...");

	// Build Changes
	chokidar
		.watch(config.watch.build, {
			ignoreInitial: true,
			ignored: [
				"pages/storyblok/**/*.html",
				"pages/storyblok/*.html",
				"data/*.json",
			],
		})
		.on("all", (event, filepath) => {
			console.log("Changes detected, building the site...");
			var buildError = false;
			// Making sure the filepath contains just slashes rather than backslashes (for Windows machines)
			filepath = filepath.replace(/\\/g, "/");
			exec(
				`node ${source}/build --file=${filepath}`,
				(error, stdout, stderr) => {
					if (error || stderr) {
						console.log(stdout);
						console.log(
							"\x1b[31m",
							"Done with errors :( Watching for changes...",
							"\x1b[0m\n"
						);
						console.log(`\x1b[31m${stderr}`, "\x1b[0m\n");
					} else {
						console.log(stdout);
						console.log(
							"\x1b[32m",
							"Done with success! Watching for changes...",
							"\x1b[0m\n"
						);
					}
				}
			);
		});

	// Assets Changes
	chokidar
		.watch(config.watch.assets, { ignoreInitial: true })
		.on("all", (event, filepath) => {
			console.log("Changes detected, compiling assets...");
			console.time("Assets Compilation Time");
			var assetsError = false;
			try {
				execSync(`node ${source}/assets`);
				HeadlessFlash.fetchAdditionalFiles();
			} catch (e) {
				assetsError = e;
			}
			console.timeEnd("Assets Compilation Time");
			for (var filepath in config.assets_compile)
				fse.copySync(filepath, config.build.folder + "/" + filepath); // Move all the assets to the build folder
			if (!assetsError)
				console.log(
					"\x1b[32m",
					"Done with success! Watching for changes...",
					"\x1b[0m"
				);
			else {
				console.log(
					"\x1b[31m",
					"Done with errors :( Watching for changes...",
					"\x1b[0m"
				);
				process.stderr.write(
					`\x1b[31m============ ### Assets Error ============\n\n${assetsError}\n\n============ ### End of Assets Error ============\n\n\n\x1b[0m`
				);
			}
		});
})();
